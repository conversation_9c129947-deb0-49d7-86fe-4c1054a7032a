package com.morohub.apsp.config;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.morohub.apsp.config.model.CountryConfig;
import com.morohub.apsp.config.model.DocumentTypeConfig;
import com.morohub.apsp.config.model.MultiCountryConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * Unified service to manage country-specific configurations for XML parsing, AS4 processing, and UBL document handling
 * Consolidates functionality from both CountryConfigurationService and MultiCountryConfigService
 */
@Service
public class CountryConfigurationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(CountryConfigurationService.class);

    @Autowired
    private ObjectMapper objectMapper;

    private JsonNode configRoot;
    private JsonNode globalDefaults;
    private JsonNode countriesConfig;
    private MultiCountryConfig structuredConfig;

    @PostConstruct
    public void loadConfiguration() {
        try {
            LOGGER.info("Loading unified country configuration...");

            ClassPathResource resource = new ClassPathResource("country-config.json");
            if (!resource.exists()) {
                throw new RuntimeException("Configuration file 'country-config.json' not found in classpath");
            }

            try (InputStream inputStream = resource.getInputStream()) {
                // Load as JsonNode for flexible access
                configRoot = objectMapper.readTree(inputStream);
                globalDefaults = configRoot.get("globalDefaults");
                countriesConfig = configRoot.get("countries");

                LOGGER.info("✅ Unified country configuration loaded successfully");
                LOGGER.info("📊 Available countries: {}", String.join(", ", getAvailableCountries()));
            }

            // Load structured config separately
            try (InputStream inputStream2 = resource.getInputStream()) {
                structuredConfig = objectMapper.readValue(inputStream2, MultiCountryConfig.class);
                logConfigurationSummary();
            }

        } catch (IOException e) {
            LOGGER.error("❌ Failed to load country configuration: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to load country configuration", e);
        }
    }
    
    /**
     * Get country and document-specific XML parsing configuration
     */
    public XmlParsingConfig getXmlParsingConfig(String countryCode, String documentType) {
        CountryAS4Config countryConfig = getCountryAS4Config(countryCode);
        DocumentTypeConfig docTypeConfig = countryConfig.getDocumentTypeConfig(documentType);

        return new XmlParsingConfig(globalDefaults, countryConfig, docTypeConfig);
    }
    
    /**
     * Get country-specific AS4 configuration
     */
    public CountryAS4Config getCountryAS4Config(String countryCode) {
        if (countriesConfig == null) {
            throw new IllegalStateException("Countries configuration not loaded");
        }
        
        JsonNode countryNode = countriesConfig.get(countryCode);
        if (countryNode == null) {
            LOGGER.warn("⚠️ Country {} not found, using DEFAULT configuration", countryCode);
            countryNode = countriesConfig.get("DEFAULT");
        }
        
        if (countryNode == null) {
            throw new IllegalStateException("DEFAULT country configuration not found");
        }
        
        return new CountryAS4Config(countryNode, countryCode);
    }
    
    /**
     * Get document type configuration for a specific country and document type
     */
    public DocumentTypeConfig getDocumentTypeConfig(String countryCode, String documentType) {
        CountryAS4Config countryConfig = getCountryAS4Config(countryCode);
        return countryConfig.getDocumentTypeConfig(documentType);
    }
    
    /**
     * Get available countries
     */
    public List<String> getAvailableCountries() {
        List<String> countries = new ArrayList<>();
        if (countriesConfig != null) {
            countriesConfig.fieldNames().forEachRemaining(countries::add);
        }
        return countries;
    }

    // ========== Methods from MultiCountryConfigService ==========

    /**
     * Get document type configuration for a specific country and document type (structured approach)
     * This method provides the same functionality as MultiCountryConfigService.getDocumentTypeConfig
     */
    public DocumentTypeConfig getDocumentTypeConfigStructured(String countryCode, String documentType) {
        if (structuredConfig == null) {
            throw new IllegalStateException("Structured configuration not loaded");
        }

        DocumentTypeConfig docConfig = structuredConfig.getDocumentTypeConfig(countryCode, documentType);

        if (docConfig == null) {
            LOGGER.warn("No configuration found for country: {} and document type: {}, using DEFAULT",
                countryCode, documentType);
            docConfig = structuredConfig.getDocumentTypeConfig("DEFAULT", documentType);
        }

        if (docConfig == null) {
            throw new IllegalArgumentException(
                String.format("No configuration found for document type: %s (country: %s)",
                    documentType, countryCode));
        }

        LOGGER.debug("Using configuration for {}/{}: {}", countryCode, documentType, docConfig);
        return docConfig;
    }

    /**
     * Get UBL class for a specific country and document type
     */
    public Class<?> getUBLClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return Class.forName(config.getClassPath());
    }

    /**
     * Get factory class for a specific country and document type
     */
    public Class<?> getFactoryClass(String countryCode, String documentType) throws ClassNotFoundException {
        DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return Class.forName(config.getFactoryPath());
    }

    /**
     * Check if country and document type combination is supported
     */
    public boolean isSupported(String countryCode, String documentType) {
        if (structuredConfig == null) {
            return false;
        }
        return structuredConfig.supportsDocumentType(countryCode, documentType) ||
               structuredConfig.supportsDocumentType("DEFAULT", documentType);
    }

    /**
     * Get all supported countries
     */
    public Set<String> getSupportedCountries() {
        if (structuredConfig == null || structuredConfig.getCountries() == null) {
            return java.util.Collections.emptySet();
        }
        return structuredConfig.getCountries().keySet();
    }

    /**
     * Reload configuration (useful for testing or runtime updates)
     */
    public void reloadConfiguration() {
        LOGGER.info("Reloading unified country configuration...");
        loadConfiguration();
    }

    /**
     * Get the full structured configuration (for debugging/admin purposes)
     */
    public MultiCountryConfig getFullConfiguration() {
        return structuredConfig;
    }

    /**
     * Get all supported document types for a country
     */
    public Set<String> getSupportedDocumentTypes(String countryCode) {
        if (structuredConfig == null) {
            return java.util.Collections.emptySet();
        }

        com.morohub.apsp.config.model.CountryConfig countryConfig = structuredConfig.getCountry(countryCode);
        if (countryConfig == null || countryConfig.getDocumentTypes() == null) {
            return java.util.Collections.emptySet();
        }

        return countryConfig.getDocumentTypes().keySet();
    }

    /**
     * Get schematron file for a specific country and document type (backward compatibility)
     */
    public String getSchematronFile(String countryCode, String documentType) {
        com.morohub.apsp.config.model.DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getSchematronFile();
    }

    /**
     * Get schematron files for a specific country and document type
     */
    public List<String> getSchematronFiles(String countryCode, String documentType) {
        com.morohub.apsp.config.model.DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getSchematronFiles();
    }

    /**
     * Get customization ID for a specific country and document type
     */
    public String getCustomizationId(String countryCode, String documentType) {
        com.morohub.apsp.config.model.DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getCustomizationId();
    }

    /**
     * Get profile ID for a specific country and document type
     */
    public String getProfileId(String countryCode, String documentType) {
        com.morohub.apsp.config.model.DocumentTypeConfig config = getDocumentTypeConfigStructured(countryCode, documentType);
        return config.getProfileId();
    }
    
    /**
     * XML Parsing Configuration wrapper that combines global defaults with country/document-specific settings
     */
    public static class XmlParsingConfig {
        private final JsonNode globalDefaults;
        private final CountryAS4Config countryConfig;
        private final DocumentTypeConfig documentConfig;

        public XmlParsingConfig(JsonNode globalDefaults, CountryAS4Config countryConfig, DocumentTypeConfig documentConfig) {
            this.globalDefaults = globalDefaults;
            this.countryConfig = countryConfig;
            this.documentConfig = documentConfig;
        }

        // Default participant IDs from country config
        public String getDefaultSenderParticipantId() {
            return countryConfig.getDefaultSenderParticipantId();
        }

        public String getDefaultReceiverParticipantId() {
            return countryConfig.getDefaultReceiverParticipantId();
        }

        public String getDefaultCountryCode() {
            return countryConfig.getCountryCode();
        }

        // Document-specific values
        public String getDefaultDocumentTypeId() {
            return documentConfig.getAS4DocumentTypeId();
        }

        public String getDefaultProfileExecutionId() {
            return documentConfig.getDefaultProfileExecutionId();
        }

        // XML element names from global defaults
        public String getSenderPartyElement() {
            return getGlobalDefault("xmlElements.senderPartyElement", "AccountingSupplierParty");
        }

        public String getReceiverPartyElement() {
            return getGlobalDefault("xmlElements.receiverPartyElement", "AccountingCustomerParty");
        }

        public String getEndpointIdElement() {
            return getGlobalDefault("xmlElements.endpointIdElement", "cbc:EndpointID");
        }

        public String getPartyIdElement() {
            return getGlobalDefault("xmlElements.partyIdElement", "cbc:ID");
        }

        public String getCustomizationIdElement() {
            return getGlobalDefault("xmlElements.customizationIdElement", "cbc:CustomizationID");
        }

        public String getProfileIdElement() {
            return getGlobalDefault("xmlElements.profileIdElement", "cbc:ProfileID");
        }

        public String getInvoiceIdElement() {
            return getGlobalDefault("xmlElements.invoiceIdElement", "cbc:ID");
        }

        public String getProfileExecutionIdElement() {
            return getGlobalDefault("xmlElements.profileExecutionIdElement", "cbc:ProfileExecutionID");
        }

        public String getIssueDateElement() {
            return getGlobalDefault("xmlElements.issueDateElement", "cbc:IssueDate");
        }

        public String getCountryCodeElement() {
            return getGlobalDefault("xmlElements.countryCodeElement", "cbc:IdentificationCode");
        }

        public String getCountrySubentityElement() {
            return getGlobalDefault("xmlElements.countrySubentityElement", "cbc:CountrySubentity");
        }

        // SBDH element names from global defaults
        public String getScopeElement() {
            return getGlobalDefault("sbdhElements.scopeElement", "Scope");
        }

        public String getTypeElement() {
            return getGlobalDefault("sbdhElements.typeElement", "Type");
        }

        public String getInstanceIdentifierElement() {
            return getGlobalDefault("sbdhElements.instanceIdentifierElement", "InstanceIdentifier");
        }

        public String getCountryC1Type() {
            return getGlobalDefault("sbdhElements.countryC1Type", "COUNTRY_C1");
        }

        // SBDH configuration combining global and document-specific
        public SbdhConfiguration getSbdhConfiguration() {
            return new SbdhConfiguration(globalDefaults, documentConfig);
        }

        private String getGlobalDefault(String path, String defaultValue) {
            JsonNode node = globalDefaults;
            for (String part : path.split("\\.")) {
                node = node.get(part);
                if (node == null) {
                    return defaultValue;
                }
            }
            return node.asText(defaultValue);
        }
    }
    
    /**
     * SBDH Configuration wrapper that combines global and document-specific settings
     */
    public static class SbdhConfiguration {
        private final JsonNode globalDefaults;
        private final DocumentTypeConfig documentConfig;

        public SbdhConfiguration(JsonNode globalDefaults, DocumentTypeConfig documentConfig) {
            this.globalDefaults = globalDefaults;
            this.documentConfig = documentConfig;
        }

        public String getHeaderVersion() {
            return getGlobalDefault("sbdhConfiguration.headerVersion", "1.0");
        }

        public String getAuthority() {
            return getGlobalDefault("sbdhConfiguration.authority", "iso6523-actorid-upis");
        }

        public String getStandard() {
            return documentConfig.getSbdhStandard();
        }

        public String getTypeVersion() {
            return documentConfig.getSbdhTypeVersion();
        }

        public String getDocumentType() {
            return documentConfig.getSbdhDocumentType();
        }

        public String getDocumentIdScopeType() {
            return getGlobalDefault("sbdhConfiguration.documentIdScope.type", "DOCUMENTID");
        }

        public String getDocumentIdScopeIdentifier() {
            return getGlobalDefault("sbdhConfiguration.documentIdScope.identifier", "busdox-docid-qns");
        }

        public String getProcessIdScopeType() {
            return getGlobalDefault("sbdhConfiguration.processIdScope.type", "PROCESSID");
        }

        public String getProcessIdScopeIdentifier() {
            return getGlobalDefault("sbdhConfiguration.processIdScope.identifier", "cenbii-procid-ubl");
        }

        private String getGlobalDefault(String path, String defaultValue) {
            JsonNode node = globalDefaults;
            for (String part : path.split("\\.")) {
                node = node.get(part);
                if (node == null) {
                    return defaultValue;
                }
            }
            return node.asText(defaultValue);
        }
    }
    
    /**
     * Country-specific AS4 Configuration wrapper
     */
    public static class CountryAS4Config {
        private final JsonNode config;
        private final String countryCode;
        
        public CountryAS4Config(JsonNode config, String countryCode) {
            this.config = config;
            this.countryCode = countryCode;
        }
        
        public String getCountryCode() {
            return config.get("countryCode").asText(countryCode);
        }
        
        public String getDefaultSenderParticipantId() {
            JsonNode as4Config = config.get("as4Config");
            if (as4Config != null && as4Config.has("defaultSenderParticipantId")) {
                return as4Config.get("defaultSenderParticipantId").asText();
            }
            return "9915:test-sender";
        }
        
        public String getDefaultReceiverParticipantId() {
            JsonNode as4Config = config.get("as4Config");
            if (as4Config != null && as4Config.has("defaultReceiverParticipantId")) {
                return as4Config.get("defaultReceiverParticipantId").asText();
            }
            return "9922:test-receiver";
        }
        
        public DocumentTypeConfig getDocumentTypeConfig(String documentType) {
            JsonNode documentTypes = config.get("documentTypes");
            if (documentTypes == null) {
                throw new IllegalStateException("Document types configuration not found for country: " + countryCode);
            }
            
            JsonNode docTypeNode = documentTypes.get(documentType);
            if (docTypeNode == null) {
                throw new IllegalArgumentException("Document type " + documentType + " not found for country: " + countryCode);
            }
            
            return new DocumentTypeConfig(docTypeNode);
        }
    }
    


    /**
     * Log configuration summary for debugging
     */
    private void logConfigurationSummary() {
        if (structuredConfig == null || structuredConfig.getCountries() == null) {
            LOGGER.warn("No countries configured");
            return;
        }

        LOGGER.info("📋 Configuration Summary:");
        structuredConfig.getCountries().forEach((countryCode, countryConfig) -> {
            LOGGER.info("  🌍 {} ({}): {} document types",
                countryCode,
                countryConfig.getName(),
                countryConfig.getDocumentTypes() != null ? countryConfig.getDocumentTypes().size() : 0);

            if (countryConfig.getDocumentTypes() != null) {
                countryConfig.getDocumentTypes().forEach((docType, docConfig) -> {
                    LOGGER.debug("    📄 {}: {} | {}",
                        docType,
                        docConfig.getClassPath(),
                        docConfig.getSchematronFile());
                });
            }
        });
    }
}
