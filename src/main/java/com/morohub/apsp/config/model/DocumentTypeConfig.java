package com.morohub.apsp.config.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import java.util.ArrayList;
import java.util.Map;

/**
 * Configuration for a specific document type (Invoice, CreditNote, etc.)
 */
public class DocumentTypeConfig {

    @JsonProperty("classPath")
    private String classPath;

    @JsonProperty("factoryPath")
    private String factoryPath;

    @JsonProperty("schematronFile")
    private String schematronFile;

    @JsonProperty("schematronFiles")
    private List<String> schematronFiles;

    @JsonProperty("customizationId")
    private String customizationId;

    @JsonProperty("profileId")
    private String profileId;

    // Default constructor
    public DocumentTypeConfig() {}

    // Constructor with all fields (backward compatibility)
    public DocumentTypeConfig(String classPath, String factoryPath, String schematronFile,
                             String customizationId, String profileId) {
        this.classPath = classPath;
        this.factoryPath = factoryPath;
        this.schematronFile = schematronFile;
        this.customizationId = customizationId;
        this.profileId = profileId;
    }

    // Constructor with multiple schematron files
    public DocumentTypeConfig(String classPath, String factoryPath, List<String> schematronFiles,
                             String customizationId, String profileId) {
        this.classPath = classPath;
        this.factoryPath = factoryPath;
        this.schematronFiles = schematronFiles;
        this.customizationId = customizationId;
        this.profileId = profileId;
    }

    // Getters and Setters
    public String getClassPath() {
        return classPath;
    }

    public void setClassPath(String classPath) {
        this.classPath = classPath;
    }

    public String getFactoryPath() {
        return factoryPath;
    }

    public void setFactoryPath(String factoryPath) {
        this.factoryPath = factoryPath;
    }

    public String getSchematronFile() {
        return schematronFile;
    }

    public void setSchematronFile(String schematronFile) {
        this.schematronFile = schematronFile;
    }

    public List<String> getSchematronFiles() {
        if (schematronFiles != null) {
            return schematronFiles;
        }
        // Backward compatibility: if schematronFiles is null but schematronFile is set
        if (schematronFile != null) {
            List<String> singleFileList = new ArrayList<>();
            singleFileList.add(schematronFile);
            return singleFileList;
        }
        return new ArrayList<>();
    }

    public void setSchematronFiles(List<String> schematronFiles) {
        this.schematronFiles = schematronFiles;
    }

    public String getCustomizationId() {
        return customizationId;
    }

    public void setCustomizationId(String customizationId) {
        this.customizationId = customizationId;
    }

    public String getProfileId() {
        return profileId;
    }

    public void setProfileId(String profileId) {
        this.profileId = profileId;
    }

    @Override
    public String toString() {
        return "DocumentTypeConfig{" +
                "classPath='" + classPath + '\'' +
                ", factoryPath='" + factoryPath + '\'' +
                ", schematronFile='" + schematronFile + '\'' +
                ", schematronFiles=" + schematronFiles +
                ", customizationId='" + customizationId + '\'' +
                ", profileId='" + profileId + '\'' +
                '}';
    }
}
