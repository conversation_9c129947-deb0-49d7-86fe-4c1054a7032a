package com.morohub.apsp.config.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.Map;

/**
 * Configuration for a specific country
 */
public class CountryConfig {

    @JsonProperty("name")
    private String name;

    @JsonProperty("countryCode")
    private String countryCode;

    @JsonProperty("as4Config")
    private Map<String, Object> as4Config;

    @JsonProperty("documentTypes")
    private Map<String, DocumentTypeConfig> documentTypes;

    // Default constructor
    public CountryConfig() {}

    // Constructor with all fields
    public CountryConfig(String name, Map<String, DocumentTypeConfig> documentTypes) {
        this.name = name;
        this.documentTypes = documentTypes;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Map<String, DocumentTypeConfig> getDocumentTypes() {
        return documentTypes;
    }

    public void setDocumentTypes(Map<String, DocumentTypeConfig> documentTypes) {
        this.documentTypes = documentTypes;
    }

    /**
     * Get document type configuration by type name
     */
    public DocumentTypeConfig getDocumentType(String documentType) {
        if (documentTypes == null) {
            return null;
        }
        return documentTypes.get(documentType.toUpperCase());
    }

    /**
     * Check if document type is supported
     */
    public boolean supportsDocumentType(String documentType) {
        return documentTypes != null && documentTypes.containsKey(documentType.toUpperCase());
    }

    @Override
    public String toString() {
        return "CountryConfig{" +
                "name='" + name + '\'' +
                ", documentTypes=" + documentTypes +
                '}';
    }
}
