package com.morohub.apsp.config.model;

import com.fasterxml.jackson.databind.JsonNode;

/**
 * XML Parsing Configuration that combines global defaults with country/document-specific settings
 */
public class XmlParsingConfig {
    private final JsonNode globalDefaults;
    private final CountryConfig countryConfig;
    private final DocumentTypeConfig documentConfig;

    public XmlParsingConfig(JsonNode globalDefaults, CountryConfig countryConfig, DocumentTypeConfig documentConfig) {
        this.globalDefaults = globalDefaults;
        this.countryConfig = countryConfig;
        this.documentConfig = documentConfig;
    }

    // Default participant IDs from country config
    public String getDefaultSenderParticipantId() {
        return countryConfig.getDefaultSenderParticipantId();
    }

    public String getDefaultReceiverParticipantId() {
        return countryConfig.getDefaultReceiverParticipantId();
    }

    public String getDefaultCountryCode() {
        return countryConfig.getCountryCode();
    }

    // Document-specific values
    public String getDefaultDocumentTypeId() {
        return documentConfig.getAs4DocumentTypeId();
    }

    public String getDefaultProfileExecutionId() {
        return documentConfig.getDefaultProfileExecutionId();
    }

    // XML element names from global defaults
    public String getSenderPartyElement() {
        return getGlobalDefault("xmlElements.senderPartyElement", "AccountingSupplierParty");
    }

    public String getReceiverPartyElement() {
        return getGlobalDefault("xmlElements.receiverPartyElement", "AccountingCustomerParty");
    }

    public String getEndpointIdElement() {
        return getGlobalDefault("xmlElements.endpointIdElement", "cbc:EndpointID");
    }

    public String getPartyIdElement() {
        return getGlobalDefault("xmlElements.partyIdElement", "cbc:ID");
    }

    public String getCustomizationIdElement() {
        return getGlobalDefault("xmlElements.customizationIdElement", "cbc:CustomizationID");
    }

    public String getProfileIdElement() {
        return getGlobalDefault("xmlElements.profileIdElement", "cbc:ProfileID");
    }

    public String getInvoiceIdElement() {
        return getGlobalDefault("xmlElements.invoiceIdElement", "cbc:ID");
    }

    public String getProfileExecutionIdElement() {
        return getGlobalDefault("xmlElements.profileExecutionIdElement", "cbc:ProfileExecutionID");
    }

    public String getIssueDateElement() {
        return getGlobalDefault("xmlElements.issueDateElement", "cbc:IssueDate");
    }

    public String getCountryCodeElement() {
        return getGlobalDefault("xmlElements.countryCodeElement", "cbc:IdentificationCode");
    }

    public String getCountrySubentityElement() {
        return getGlobalDefault("xmlElements.countrySubentityElement", "cbc:CountrySubentity");
    }

    // SBDH element names from global defaults
    public String getScopeElement() {
        return getGlobalDefault("sbdhElements.scopeElement", "Scope");
    }

    public String getTypeElement() {
        return getGlobalDefault("sbdhElements.typeElement", "Type");
    }

    public String getInstanceIdentifierElement() {
        return getGlobalDefault("sbdhElements.instanceIdentifierElement", "InstanceIdentifier");
    }

    public String getCountryC1Type() {
        return getGlobalDefault("sbdhElements.countryC1Type", "COUNTRY_C1");
    }

    // SBDH configuration combining global and document-specific
    public SbdhConfiguration getSbdhConfiguration() {
        return new SbdhConfiguration(globalDefaults, documentConfig);
    }

    private String getGlobalDefault(String path, String defaultValue) {
        JsonNode node = globalDefaults;
        for (String part : path.split("\\.")) {
            node = node.get(part);
            if (node == null) {
                return defaultValue;
            }
        }
        return node.asText(defaultValue);
    }
}
