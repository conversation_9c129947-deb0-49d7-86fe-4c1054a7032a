package com.morohub.apsp.core.service;

import com.morohub.apsp.common.dto.RecipientEndpointResponseDTO;
import com.morohub.apsp.config.CountryConfigurationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;

/**
 * Generic Invoice Service using dynamic class loading
 * Handles both forward and reverse flow invoice processing
 */
@Service
public class InvoiceService {

    private static final Logger logger = LoggerFactory.getLogger(InvoiceService.class);

    @Autowired
    private RecipientService recipientService;

    @Autowired
    private org.springframework.web.client.RestTemplate restTemplate;

    @Autowired
    private XmlGeneratorService xmlGeneratorService;

    @Autowired
    private ValidationService validationService;



    @Autowired
    private CountryConfigurationService countryConfigurationService;

    @Autowired
    private SchematronValidationService schematronValidationService;

    @Autowired
    private XmlJsonConversionService xmlJsonConversionService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    // Configuration for response handling
    @Value("${reverse.flow.response.send.enabled:true}")
    private boolean responseSendEnabled;

    @Value("${reverse.flow.response.file.enabled:false}")
    private boolean responseFileEnabled;

    @Value("${reverse.flow.response.file.path:responses/}")
    private String responseFilePath;

    /**
     * Generate invoice XML from request object using dynamic class loading
     */
    public String generateInvoice(Object request) throws Exception {
        try {
            logger.info("Starting invoice generation process using dynamic class loading");

            // Convert request to XML using dynamic class loading
            String invoiceXml = xmlGeneratorService.generateInvoice(request);

            // Validate the generated XML
            boolean isValid = validationService.validateInvoiceXml(invoiceXml);
            if (!isValid) {
                throw new IllegalArgumentException("Generated invoice XML failed validation");
            }

            logger.info("Invoice XML generated and validated successfully");
            return invoiceXml;

        } catch (Exception e) {
            logger.error("Failed to generate invoice", e);
            throw new RuntimeException("Invoice generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Generate UBL Invoice XML with country-specific configuration
     */
    public String generateInvoiceWithConfig(Object request, String countryCode, String documentType) throws Exception {
        try {
            logger.info("Generating invoice XML for country: {} and document type: {}", countryCode, documentType);

            // Generate XML using country-specific configuration
            String invoiceXml = xmlGeneratorService.generateInvoiceWithConfig(request, countryCode, documentType);

            // Validate using country-specific schematron files
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles(countryCode, documentType);
            boolean isValid = validationService.validateXmlWithMultipleSchematrons(invoiceXml, schematronFiles);
            if (!isValid) {
                throw new RuntimeException("Generated invoice XML failed country-specific validation");
            }

            logger.info("Invoice XML generated and validated successfully for {}/{}", countryCode, documentType);
            return invoiceXml;

        } catch (Exception e) {
            logger.error("Failed to generate invoice for {}/{}", countryCode, documentType, e);
            throw new RuntimeException("Invoice generation failed: " + e.getMessage(), e);
        }
    }

    /**
     * Process UBL document (InvoiceType, CreditNoteType, etc.) and send via AS4
     */
    public String processInvoice(Object ublDocument) throws Exception {
        try {
            logger.info("Processing UBL document: {}", ublDocument.getClass().getSimpleName());

            // Generate XML using dynamic class loading
            String invoiceXml = generateInvoice(ublDocument);

            // Extract recipient information from UBL document using reflection
            String recipientId = extractRecipientIdFromUBL(ublDocument);
            if (recipientId != null) {
                RecipientEndpointResponseDTO endpoint =
                    recipientService.getRecipientEndpoint(recipientId);

                logger.info("Retrieved recipient endpoint: {} for participant: {}",
                    endpoint.getEndpointUrl(), endpoint.getParticipantId());

                // Send via normal REST POST call
                sendViaRestPost(invoiceXml, recipientId, endpoint.getEndpointUrl());

                logger.info("UBL document sent successfully via REST POST");
            } else {
                logger.warn("No recipient information found in UBL document, skipping sending");
            }

            return invoiceXml;

        } catch (Exception e) {
            logger.error("Failed to process UBL document", e);
            throw new RuntimeException("UBL document processing failed: " + e.getMessage(), e);
        }
    }




    /**
     * Process incoming UBL XML message with country-specific validation
     */
    public String processIncomingMessageWithCountryConfig(String ublXml, String countryCode, String documentType) throws Exception {
        try {
            logger.info("Processing incoming UBL XML for country: {} and document type: {}", countryCode, documentType);

            // Convert UBL XML to object using country-specific configuration
            Object incomingUblDocument = parseUBLXmlToObject(ublXml, countryCode, documentType);


            // Generate response document using country-specific configuration
            //String responseXml = xmlGeneratorService.generateInvoiceResponseXmlWithConfig(incomingUblDocument, countryCode, documentType);

            // Handle response with file writing as default for processIncomingAS4Message
            handleResponseWithFileDefault(ublXml, incomingUblDocument, countryCode, documentType);

            return ublXml;

        } catch (Exception e) {
            logger.error("Failed to process incoming message for {}/{}", countryCode, documentType, e);
            throw new RuntimeException("Message processing failed: " + e.getMessage(), e);
        }
    }




    /**
     * Parse UBL XML to object using country-specific configuration
     */
    private Object parseUBLXmlToObject(String ublXml, String countryCode, String documentType) throws Exception {
        logger.debug("Parsing UBL XML to object using country-specific configuration: {}/{}", countryCode, documentType);

        try {
            // Get country-specific UBL class
            Class<?> ublClass = countryConfigurationService.getUBLClass(countryCode, documentType);
            logger.info("🔍 Using UBL class for parsing: {} for {}/{}", ublClass.getName(), countryCode, documentType);

            // Log XML structure for debugging
            logger.debug("🔍 UBL XML to parse (first 1000 chars): {}", ublXml.substring(0, Math.min(1000, ublXml.length())));

            // Check if XML starts with proper root element
            String trimmedXml = ublXml.trim();
            if (trimmedXml.startsWith("<?xml")) {
                int rootStart = trimmedXml.indexOf('<', 5); // Skip XML declaration
                if (rootStart > 0) {
                    int rootEnd = trimmedXml.indexOf('>', rootStart);
                    if (rootEnd > 0) {
                        String rootElement = trimmedXml.substring(rootStart, rootEnd + 1);
                        logger.debug("🔍 Root element: {}", rootElement);
                    }
                }
            } else if (trimmedXml.startsWith("<")) {
                int rootEnd = trimmedXml.indexOf('>');
                if (rootEnd > 0) {
                    String rootElement = trimmedXml.substring(0, rootEnd + 1);
                    logger.debug("🔍 Root element: {}", rootElement);
                }
            }

            // Validate XML structure before parsing
            if (!isValidUBLStructure(ublXml, documentType)) {
                logger.warn("⚠️ Invalid UBL structure detected, attempting to fix...");
                ublXml = fixUBLStructure(ublXml, documentType);
            }

            // Convert XML to UBL object
            try {
                return xmlJsonConversionService.xmlToObject(ublXml, ublClass);
            } catch (jakarta.xml.bind.UnmarshalException e) {
                if (e.getMessage().contains("CommonExtensionComponents")) {
                    logger.error("🔍 JAXB expecting CommonExtensionComponents but found Invoice element");
                    logger.error("🔍 This suggests the JAXB context was created for the wrong class or XML structure is invalid");
                    logger.error("🔍 UBL Class being used: {}", ublClass.getName());
                    logger.error("🔍 Document type: {}, Country: {}", documentType, countryCode);

                    // Try to create a simple test to see what JAXB expects
                    logger.error("🔍 Attempting alternative parsing approach...");
                    return attemptAlternativeParsing(ublXml, ublClass, countryCode, documentType);
                }
                throw e;
            }

        } catch (Exception e) {
            logger.error("Failed to parse UBL XML to object for {}/{}", countryCode, documentType, e);
            logger.error("🔍 XML that failed to parse: {}", ublXml.substring(0, Math.min(2000, ublXml.length())));
            throw new RuntimeException("UBL XML parsing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Handle response with file writing as default option
     * This method prioritizes file writing and optionally sends via REST
     */
    private void handleResponseWithFileDefault(String responseXml, Object incomingUblDocument, String countryCode, String documentType) throws Exception {
        try {
            logger.info("🔄 Handling response for {}/{} with file writing as default", countryCode, documentType);

            // Default behavior: Always write to file
            writeResponseToFile(responseXml, countryCode, documentType);

            // Also write the incoming UBL document to file
            //writeIncomingUblDocumentToFile(incomingUblDocument, countryCode, documentType);

            // Optional: Also send via REST if explicitly enabled
            if (responseSendEnabled) {
                String senderId = extractSenderIdFromUBL(incomingUblDocument);
                if (senderId != null) {
                    RecipientEndpointResponseDTO senderEndpoint =
                        recipientService.getRecipientEndpoint(senderId);

                    logger.info("📤 Also sending response back to sender: {} at endpoint: {}",
                        senderEndpoint.getParticipantId(), senderEndpoint.getEndpointUrl());

                    // Send response back via normal REST POST call
                    sendViaRestPost(responseXml, senderId, senderEndpoint.getEndpointUrl());

                    logger.info("✅ Response sent successfully via REST POST for {}/{}", countryCode, documentType);
                } else {
                    logger.warn("⚠️ No sender ID found in UBL document, cannot send response");
                }
            }

            logger.info("✅ Response successfully written to file{}",
                       responseSendEnabled ? " and sent via REST" : "");

        } catch (Exception e) {
            logger.error("❌ Failed to handle response for {}/{}", countryCode, documentType, e);
            throw new Exception("Response handling failed: " + e.getMessage(), e);
        }
    }

    /**
     * Handle response based on configuration - either send via REST or write to file
     */
    private void handleResponse(String responseXml, Object incomingUblDocument, String countryCode, String documentType) throws Exception {
        try {
            logger.info("🔄 Handling response for {}/{} - Send enabled: {}, File enabled: {}",
                       countryCode, documentType, responseSendEnabled, responseFileEnabled);

            // Option 1: Write response to file (if enabled)
            if (responseFileEnabled) {
                writeResponseToFile(responseXml, countryCode, documentType);
                // Also write the incoming UBL document to file
                writeIncomingUblDocumentToFile(incomingUblDocument, countryCode, documentType);
            }

            // Option 2: Send response via REST POST (if enabled)
            if (responseSendEnabled) {
                String senderId = extractSenderIdFromUBL(incomingUblDocument);
                if (senderId != null) {
                    RecipientEndpointResponseDTO senderEndpoint =
                        recipientService.getRecipientEndpoint(senderId);

                    logger.info("📤 Sending response back to sender: {} at endpoint: {}",
                        senderEndpoint.getParticipantId(), senderEndpoint.getEndpointUrl());

                    // Send response back via normal REST POST call
                    sendViaRestPost(responseXml, senderId, senderEndpoint.getEndpointUrl());

                    logger.info("✅ Response sent successfully via REST POST reverse flow for {}/{}", countryCode, documentType);
                } else {
                    logger.warn("⚠️ No sender ID found in UBL document, cannot send response");
                }
            }

            // Log what was done
            if (!responseSendEnabled && !responseFileEnabled) {
                logger.warn("⚠️ Both response sending and file writing are disabled - response will be lost");
            } else {
                String actions = "";
                if (responseFileEnabled) actions += "written to file";
                if (responseSendEnabled) {
                    if (!actions.isEmpty()) actions += " and ";
                    actions += "sent via REST";
                }
                logger.info("✅ Response successfully {}", actions);
            }

        } catch (Exception e) {
            logger.error("❌ Failed to handle response for {}/{}", countryCode, documentType, e);
            throw new Exception("Response handling failed: " + e.getMessage(), e);
        }
    }

    /**
     * Write response XML to file at configured location
     */
    private void writeResponseToFile(String responseXml, String countryCode, String documentType) throws Exception {
        try {
            // Create directory if it doesn't exist
            Path dirPath = Paths.get(responseFilePath);
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("📁 Created response directory: {}", dirPath.toAbsolutePath());
            }

            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String filename = String.format("response_%s_%s_%s.xml", countryCode, documentType, timestamp);
            Path filePath = dirPath.resolve(filename);

            // Write response to file
            try (FileWriter writer = new FileWriter(filePath.toFile(), StandardCharsets.UTF_8)) {
                writer.write(responseXml);
            }

            logger.info("📄 Response written to file: {}", filePath.toAbsolutePath());
            logger.info("📊 File size: {} bytes", Files.size(filePath));

        } catch (Exception e) {
            logger.error("❌ Failed to write response to file", e);
            throw new Exception("File writing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Write incoming UBL document to file at configured location
     */
    private void writeIncomingUblDocumentToFile(Object incomingUblDocument, String countryCode, String documentType) throws Exception {
        try {
            // Create directory if it doesn't exist
            Path dirPath = Paths.get(responseFilePath, "incoming");
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("📁 Created incoming documents directory: {}", dirPath.toAbsolutePath());
            }

            // Convert UBL object to XML string
            String ublXml = convertUblObjectToXml(incomingUblDocument, countryCode, documentType);

            // Generate filename with timestamp
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String filename = String.format("incoming_%s_%s_%s.xml", countryCode, documentType, timestamp);
            Path filePath = dirPath.resolve(filename);

            // Write incoming UBL document to file
            try (FileWriter writer = new FileWriter(filePath.toFile(), StandardCharsets.UTF_8)) {
                writer.write(ublXml);
            }

            logger.info("📄 Incoming UBL document written to file: {}", filePath.toAbsolutePath());
            logger.info("📊 File size: {} bytes", Files.size(filePath));

        } catch (Exception e) {
            logger.error("❌ Failed to write incoming UBL document to file", e);
            throw new Exception("Incoming document file writing failed: " + e.getMessage(), e);
        }
    }

    /**
     * Convert UBL object back to XML string
     */
    private String convertUblObjectToXml(Object ublObject, String countryCode, String documentType) throws Exception {
        try {
            logger.debug("Converting UBL object back to XML for {}/{}", countryCode, documentType);

            // Get country-specific UBL class and factory
            Class<?> ublClass = countryConfigurationService.getUBLClass(countryCode, documentType);
            Class<?> factoryClass = countryConfigurationService.getFactoryClass(countryCode, documentType);

            // Create factory instance
            Object factoryInstance = factoryClass.getDeclaredConstructor().newInstance();

            // Determine the factory method name based on document type
            String methodName = getFactoryMethodName(documentType);
            java.lang.reflect.Method createMethod = factoryClass.getMethod(methodName, ublClass);

            // Create JAXBElement
            jakarta.xml.bind.JAXBElement<?> jaxbElement = (jakarta.xml.bind.JAXBElement<?>) createMethod.invoke(factoryInstance, ublObject);

            // Convert to XML
            return xmlJsonConversionService.objectToXml(jaxbElement, ublClass);

        } catch (Exception e) {
            logger.error("❌ Failed to convert UBL object to XML: {}", e.getMessage());
            throw new Exception("UBL object to XML conversion failed: " + e.getMessage(), e);
        }
    }

    /**
     * Get the appropriate factory method name based on document type
     */
    private String getFactoryMethodName(String documentType) {
        switch (documentType.toUpperCase()) {
            case "INVOICE":
                return "createInvoice";
            case "CREDITNOTE":
                return "createCreditNote";
            case "APPLICATIONRESPONSE":
                return "createApplicationResponse";
            default:
                throw new IllegalArgumentException("Unsupported document type: " + documentType);
        }
    }

    /**
     * Send XML document via normal REST POST call
     */
    private void sendViaRestPost(String xmlContent, String recipientId, String endpointUrl) throws Exception {
        try {
            logger.info("Sending XML document via REST POST to: {} for recipient: {}", endpointUrl, recipientId);

            // Prepare headers
            org.springframework.http.HttpHeaders headers = new org.springframework.http.HttpHeaders();
            headers.setContentType(org.springframework.http.MediaType.APPLICATION_XML);
            headers.set("X-Recipient-ID", recipientId);
            headers.set("X-Message-Type", "UBL-Document");

            // Create HTTP entity
            org.springframework.http.HttpEntity<String> entity =
                new org.springframework.http.HttpEntity<>(xmlContent, headers);

            // Send POST request
            org.springframework.http.ResponseEntity<String> response =
                restTemplate.postForEntity(endpointUrl, entity, String.class);

            if (response.getStatusCode().is2xxSuccessful()) {
                logger.info("Successfully sent XML document via REST POST. Status: {}", response.getStatusCode());
                if (response.getBody() != null && !response.getBody().isEmpty()) {
                    logger.debug("Response body: {}", response.getBody());
                }
            } else {
                logger.error("Failed to send XML document via REST POST. Status: {}", response.getStatusCode());
                throw new RuntimeException("REST POST failed with status: " + response.getStatusCode());
            }

        } catch (Exception e) {
            logger.error("Error sending XML document via REST POST to: {}", endpointUrl, e);
            throw new RuntimeException("Failed to send via REST POST: " + e.getMessage(), e);
        }
    }

    /**
     * Extract recipient ID from UBL document using reflection
     */
    private String extractRecipientIdFromUBL(Object ublDocument) {
        try {
            logger.debug("Extracting recipient ID from UBL document: {}", ublDocument.getClass().getSimpleName());

            // Try to get AccountingCustomerParty using reflection
            java.lang.reflect.Method getCustomerPartyMethod = findMethod(ublDocument.getClass(), "getAccountingCustomerParty");
            if (getCustomerPartyMethod != null) {
                Object customerParty = getCustomerPartyMethod.invoke(ublDocument);
                if (customerParty != null) {
                    return extractEndpointIdFromParty(customerParty);
                }
            }

            logger.debug("No recipient ID found in UBL document");
            return null;

        } catch (Exception e) {
            logger.error("Error extracting recipient ID from UBL document", e);
            return null;
        }
    }

    /**
     * Extract sender ID from UBL document using reflection
     */
    private String extractSenderIdFromUBL(Object ublDocument) {
        try {
            logger.debug("Extracting sender ID from UBL document: {}", ublDocument.getClass().getSimpleName());

            // Try to get AccountingSupplierParty using reflection
            java.lang.reflect.Method getSupplierPartyMethod = findMethod(ublDocument.getClass(), "getAccountingSupplierParty");
            if (getSupplierPartyMethod != null) {
                Object supplierParty = getSupplierPartyMethod.invoke(ublDocument);
                if (supplierParty != null) {
                    return extractEndpointIdFromParty(supplierParty);
                }
            }

            logger.debug("No sender ID found in UBL document");
            return null;

        } catch (Exception e) {
            logger.error("Error extracting sender ID from UBL document", e);
            return null;
        }
    }

    /**
     * Extract endpoint ID from party object using reflection
     */
    private String extractEndpointIdFromParty(Object party) {
        try {
            // Try to get Party from AccountingSupplierParty/AccountingCustomerParty
            java.lang.reflect.Method getPartyMethod = findMethod(party.getClass(), "getParty");
            if (getPartyMethod != null) {
                Object partyObject = getPartyMethod.invoke(party);
                if (partyObject != null) {
                    // Try to get EndpointID
                    java.lang.reflect.Method getEndpointIdMethod = findMethod(partyObject.getClass(), "getEndpointID");
                    if (getEndpointIdMethod != null) {
                        Object endpointId = getEndpointIdMethod.invoke(partyObject);
                        if (endpointId != null) {
                            // EndpointID might have a getValue() method
                            java.lang.reflect.Method getValueMethod = findMethod(endpointId.getClass(), "getValue");
                            if (getValueMethod != null) {
                                Object value = getValueMethod.invoke(endpointId);
                                return value != null ? value.toString() : null;
                            } else {
                                return endpointId.toString();
                            }
                        }
                    }
                }
            }

            return null;

        } catch (Exception e) {
            logger.error("Error extracting endpoint ID from party", e);
            return null;
        }
    }

    /**
     * Find method by name using reflection
     */
    private java.lang.reflect.Method findMethod(Class<?> clazz, String methodName) {
        try {
            return clazz.getMethod(methodName);
        } catch (NoSuchMethodException e) {
            // Try with different variations
            for (java.lang.reflect.Method method : clazz.getMethods()) {
                if (method.getName().equals(methodName) && method.getParameterCount() == 0) {
                    return method;
                }
            }
            return null;
        }
    }

    /**
     * Attempt alternative parsing when standard JAXB fails
     */
    private Object attemptAlternativeParsing(String ublXml, Class<?> ublClass, String countryCode, String documentType) throws Exception {
        logger.info("🔄 Attempting alternative parsing approach for {}/{}", countryCode, documentType);

        try {
            // Try to use the ObjectFactory approach
            String factoryClassName = ublClass.getPackage().getName() + ".ObjectFactory";
            logger.debug("🔍 Trying ObjectFactory: {}", factoryClassName);

            Class<?> factoryClass = Class.forName(factoryClassName);
            Object factory = factoryClass.getDeclaredConstructor().newInstance();

            // Use the factory to create a JAXBElement and then unmarshal
            jakarta.xml.bind.JAXBContext context = jakarta.xml.bind.JAXBContext.newInstance(ublClass);
            jakarta.xml.bind.Unmarshaller unmarshaller = context.createUnmarshaller();

            // Try unmarshalling as JAXBElement
            java.io.StringReader reader = new java.io.StringReader(ublXml);
            jakarta.xml.bind.JAXBElement<?> jaxbElement = (jakarta.xml.bind.JAXBElement<?>) unmarshaller.unmarshal(reader);

            logger.info("✅ Alternative parsing successful using JAXBElement approach");
            return jaxbElement.getValue();

        } catch (Exception e) {
            logger.error("❌ Alternative parsing also failed: {}", e.getMessage());

            // Last resort: try to manually fix the XML structure
            logger.info("🔄 Attempting manual XML structure fix...");
            String fixedXml = manuallyFixXMLStructure(ublXml, documentType);

            if (!fixedXml.equals(ublXml)) {
                logger.info("🔧 Applied manual XML fixes, retrying parsing...");
                return xmlJsonConversionService.xmlToObject(fixedXml, ublClass);
            }

            throw new RuntimeException("All parsing attempts failed", e);
        }
    }

    /**
     * Manually fix XML structure issues that might cause JAXB problems
     */
    private String manuallyFixXMLStructure(String ublXml, String documentType) {
        String fixed = ublXml;

        try {
            // Ensure proper root element structure
            String expectedRoot = getExpectedRootElement(documentType);

            // Check if the XML has the correct namespace on the root element
            if (!fixed.contains("xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:" + expectedRoot + "-2\"")) {
                // Add the missing namespace declaration
                String rootPattern = "<" + expectedRoot + "(?![^>]*xmlns=)";
                String replacement = "<" + expectedRoot + " xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:" + expectedRoot + "-2\"";
                fixed = fixed.replaceFirst(rootPattern, replacement);
                logger.debug("🔧 Added missing namespace declaration to root element");
            }

            return fixed;

        } catch (Exception e) {
            logger.warn("⚠️ Manual XML structure fix failed: {}", e.getMessage());
            return ublXml;
        }
    }

    /**
     * Validate if UBL XML has the correct structure for the document type
     */
    private boolean isValidUBLStructure(String ublXml, String documentType) {
        try {
            String expectedRootElement = getExpectedRootElement(documentType);
            String trimmedXml = ublXml.trim();

            // Check if XML starts with expected root element
            if (trimmedXml.contains("<" + expectedRootElement)) {
                logger.debug("✅ Found expected root element: {}", expectedRootElement);
                return true;
            }

            logger.warn("⚠️ Expected root element '{}' not found in XML", expectedRootElement);
            return false;

        } catch (Exception e) {
            logger.error("❌ Error validating UBL structure: {}", e.getMessage());
            return false;
        }
    }

    /**
     * Fix UBL XML structure issues
     */
    private String fixUBLStructure(String ublXml, String documentType) {
        try {
            logger.info("🔧 Attempting to fix UBL XML structure for document type: {}", documentType);

            // Remove any XML declaration and whitespace
            String cleanXml = ublXml.trim();
            if (cleanXml.startsWith("<?xml")) {
                int xmlDeclEnd = cleanXml.indexOf("?>") + 2;
                cleanXml = cleanXml.substring(xmlDeclEnd).trim();
            }

            // Ensure proper XML declaration
            String fixedXml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" + cleanXml;

            // Apply namespace fixes
            fixedXml = fixNamespaces(fixedXml);

            logger.debug("🔧 Fixed UBL XML structure");
            return fixedXml;

        } catch (Exception e) {
            logger.error("❌ Error fixing UBL structure: {}", e.getMessage());
            return ublXml; // Return original if fixing fails
        }
    }

    /**
     * Get expected root element for document type
     */
    private String getExpectedRootElement(String documentType) {
        switch (documentType.toUpperCase()) {
            case "INVOICE":
                return "Invoice";
            case "CREDITNOTE":
                return "CreditNote";
            case "APPLICATIONRESPONSE":
                return "ApplicationResponse";
            default:
                return "Invoice"; // Default fallback
        }
    }

    /**
     * Fix namespace issues in UBL XML
     */
    private String fixNamespaces(String xml) {
        // Apply similar fixes as in Phase4AS4ReceiverService
        return xml
            .replaceFirst("xmlns(:ns4)?=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\"",
                          "xmlns=\"urn:oasis:names:specification:ubl:schema:xsd:Invoice-2\"")
            .replaceFirst("xmlns:ns3=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\"",
                          "xmlns:cac=\"urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2\"")
            .replaceFirst("xmlns:ns2=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\"",
                          "xmlns:cec=\"urn:oasis:names:specification:ubl:schema:xsd:CommonExtensionComponents-2\"")
            .replaceFirst("xmlns(:ns1)?=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"",
                          "xmlns:cbc=\"urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2\"")
            .replaceAll("<ns4:", "<")
            .replaceAll("</ns4:", "</")
            .replaceAll("<ns3:", "<cac:")
            .replaceAll("</ns3:", "</cac:")
            .replaceAll("<ns2:", "<cec:")
            .replaceAll("</ns2:", "</cec:")
            .replaceAll("<ns1:", "<cbc:")
            .replaceAll("</ns1:", "</cbc:");
    }


}
